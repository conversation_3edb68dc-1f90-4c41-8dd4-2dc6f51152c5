import 'package:flutter/material.dart';

/// ثيم التطبيق والألوان
class AppTheme {
  // الألوان الأساسية
  static const Color primaryColor = Color(0xFF2E7D32); // أخضر داكن
  static const Color primaryLightColor = Color(0xFF60AD5E); // أخضر فاتح
  static const Color primaryDarkColor = Color(0xFF005005); // أخضر غامق
  
  static const Color secondaryColor = Color(0xFF1976D2); // أزرق
  static const Color secondaryLightColor = Color(0xFF63A4FF); // أزرق فاتح
  static const Color secondaryDarkColor = Color(0xFF004BA0); // أزرق غامق

  // ألوان الخلفية
  static const Color backgroundColor = Color(0xFFF5F5F5); // رمادي فاتح جداً
  static const Color surfaceColor = Color(0xFFFFFFFF); // أبيض
  static const Color cardColor = Color(0xFFFFFFFF); // أبيض

  // ألوان النص
  static const Color textPrimaryColor = Color(0xFF212121); // رمادي غامق
  static const Color textSecondaryColor = Color(0xFF757575); // رمادي متوسط
  static const Color textHintColor = Color(0xFFBDBDBD); // رمادي فاتح

  // ألوان الحالة
  static const Color successColor = Color(0xFF4CAF50); // أخضر
  static const Color warningColor = Color(0xFFFF9800); // برتقالي
  static const Color errorColor = Color(0xFFF44336); // أحمر
  static const Color infoColor = Color(0xFF2196F3); // أزرق

  // ألوان المديونية
  static const Color activeDebtColor = Color(0xFFFF5722); // برتقالي محمر
  static const Color paidDebtColor = Color(0xFF4CAF50); // أخضر
  static const Color cancelledDebtColor = Color(0xFF9E9E9E); // رمادي

  // الثيم الفاتح
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: const ColorScheme.light(
        primary: primaryColor,
        secondary: secondaryColor,
        surface: surfaceColor,
        background: backgroundColor,
        error: errorColor,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: textPrimaryColor,
        onBackground: textPrimaryColor,
        onError: Colors.white,
      ),
      
      // شريط التطبيق
      appBarTheme: const AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),

      // البطاقات
      cardTheme: CardTheme(
        color: cardColor,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),

      // الأزرار المرفوعة
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // الأزرار المحددة
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryColor,
          side: const BorderSide(color: primaryColor),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // الأزرار النصية
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryColor,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // حقول الإدخال
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: Colors.grey[50],
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: errorColor),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        hintStyle: const TextStyle(color: textHintColor),
        labelStyle: const TextStyle(color: textSecondaryColor),
      ),

      // الأيقونات
      iconTheme: const IconThemeData(
        color: textSecondaryColor,
        size: 24,
      ),

      // القوائم
      listTileTheme: const ListTileThemeData(
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        titleTextStyle: TextStyle(
          color: textPrimaryColor,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        subtitleTextStyle: TextStyle(
          color: textSecondaryColor,
          fontSize: 14,
        ),
      ),

      // الحوارات
      dialogTheme: DialogTheme(
        backgroundColor: surfaceColor,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        titleTextStyle: const TextStyle(
          color: textPrimaryColor,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
        contentTextStyle: const TextStyle(
          color: textSecondaryColor,
          fontSize: 16,
        ),
      ),

      // شريط التنقل السفلي
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: surfaceColor,
        selectedItemColor: primaryColor,
        unselectedItemColor: textSecondaryColor,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),

      // الخط الافتراضي
      fontFamily: 'Cairo', // سيتم إضافته لاحقاً
    );
  }

  // الثيم الداكن (للمستقبل)
  static ThemeData get darkTheme {
    return lightTheme.copyWith(
      colorScheme: const ColorScheme.dark(
        primary: primaryLightColor,
        secondary: secondaryLightColor,
        surface: Color(0xFF121212),
        background: Color(0xFF000000),
        error: errorColor,
      ),
    );
  }
}
