import '../../constants/app_constants.dart';
import '../../models/debt.dart';
import 'database_helper.dart';

/// خدمة إدارة المديونيات في قاعدة البيانات
class DebtsService {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// إضافة مديونية جديدة
  Future<int> addDebt(Debt debt) async {
    final debtMap = debt.toMap();
    debtMap.remove('id'); // إزالة المعرف لأنه سيتم إنشاؤه تلقائياً
    return await _dbHelper.insert(AppConstants.debtsTable, debtMap);
  }

  /// تحديث مديونية موجودة
  Future<int> updateDebt(Debt debt) async {
    if (debt.id == null) {
      throw ArgumentError('Debt ID cannot be null for update operation');
    }
    
    final debtMap = debt.toMap();
    return await _dbHelper.update(
      AppConstants.debtsTable,
      debtMap,
      'id = ?',
      [debt.id],
    );
  }

  /// حذف مديونية
  Future<int> deleteDebt(int debtId) async {
    return await _dbHelper.delete(
      AppConstants.debtsTable,
      'id = ?',
      [debtId],
    );
  }

  /// الحصول على مديونية بالمعرف
  Future<Debt?> getDebtById(int debtId) async {
    final results = await _dbHelper.query(
      AppConstants.debtsTable,
      where: 'id = ?',
      whereArgs: [debtId],
    );

    if (results.isNotEmpty) {
      return Debt.fromMap(results.first);
    }
    return null;
  }

  /// الحصول على جميع المديونيات
  Future<List<Debt>> getAllDebts({
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final results = await _dbHelper.query(
      AppConstants.debtsTable,
      orderBy: orderBy ?? 'date DESC',
      limit: limit,
      offset: offset,
    );

    return results.map((map) => Debt.fromMap(map)).toList();
  }

  /// الحصول على مديونيات شخص معين
  Future<List<Debt>> getDebtsByPerson(
    int personId,
    String personType, {
    String? status,
    String? orderBy,
    int? limit,
  }) async {
    String whereClause = 'person_id = ? AND person_type = ?';
    List<dynamic> whereArgs = [personId, personType];

    if (status != null && status.isNotEmpty) {
      whereClause += ' AND status = ?';
      whereArgs.add(status);
    }

    final results = await _dbHelper.query(
      AppConstants.debtsTable,
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: orderBy ?? 'date DESC',
      limit: limit,
    );

    return results.map((map) => Debt.fromMap(map)).toList();
  }

  /// الحصول على المديونيات النشطة
  Future<List<Debt>> getActiveDebts({
    String? personType,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    String whereClause = 'status = ?';
    List<dynamic> whereArgs = [AppConstants.debtStatusActive];

    if (personType != null && personType.isNotEmpty) {
      whereClause += ' AND person_type = ?';
      whereArgs.add(personType);
    }

    final results = await _dbHelper.query(
      AppConstants.debtsTable,
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: orderBy ?? 'date DESC',
      limit: limit,
      offset: offset,
    );

    return results.map((map) => Debt.fromMap(map)).toList();
  }

  /// البحث في المديونيات
  Future<List<Debt>> searchDebts(
    String searchTerm, {
    String? personType,
    String? status,
    String? orderBy,
    int? limit,
  }) async {
    String additionalWhere = '';
    List<dynamic> additionalWhereArgs = [];

    if (personType != null && personType.isNotEmpty) {
      additionalWhere += 'person_type = ?';
      additionalWhereArgs.add(personType);
    }

    if (status != null && status.isNotEmpty) {
      if (additionalWhere.isNotEmpty) additionalWhere += ' AND ';
      additionalWhere += 'status = ?';
      additionalWhereArgs.add(status);
    }

    final results = await _dbHelper.search(
      AppConstants.debtsTable,
      searchTerm,
      searchColumns: ['notes'],
      additionalWhere: additionalWhere.isNotEmpty ? additionalWhere : null,
      additionalWhereArgs: additionalWhereArgs.isNotEmpty ? additionalWhereArgs : null,
      orderBy: orderBy ?? 'date DESC',
      limit: limit,
    );

    return results.map((map) => Debt.fromMap(map)).toList();
  }

  /// الحصول على المديونيات مع تفاصيل الأشخاص
  Future<List<Map<String, dynamic>>> getDebtsWithPersonDetails({
    String? personType,
    String? status,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (personType != null && personType.isNotEmpty) {
      whereClause += 'd.person_type = ?';
      whereArgs.add(personType);
    }

    if (status != null && status.isNotEmpty) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'd.status = ?';
      whereArgs.add(status);
    }

    final sql = '''
      SELECT 
        d.*,
        CASE 
          WHEN d.person_type = '${AppConstants.personTypeCustomer}' THEN c.name
          WHEN d.person_type = '${AppConstants.personTypeEmployee}' THEN e.name
        END as person_name,
        CASE 
          WHEN d.person_type = '${AppConstants.personTypeCustomer}' THEN c.phone
          WHEN d.person_type = '${AppConstants.personTypeEmployee}' THEN e.phone
        END as person_phone,
        CASE 
          WHEN d.person_type = '${AppConstants.personTypeCustomer}' THEN c.email
          WHEN d.person_type = '${AppConstants.personTypeEmployee}' THEN e.email
        END as person_email
      FROM ${AppConstants.debtsTable} d
      LEFT JOIN ${AppConstants.customersTable} c ON d.person_id = c.id AND d.person_type = '${AppConstants.personTypeCustomer}'
      LEFT JOIN ${AppConstants.employeesTable} e ON d.person_id = e.id AND d.person_type = '${AppConstants.personTypeEmployee}'
      ${whereClause.isNotEmpty ? 'WHERE $whereClause' : ''}
      ORDER BY ${orderBy ?? 'd.date DESC'}
      ${limit != null ? 'LIMIT $limit' : ''}
      ${offset != null ? 'OFFSET $offset' : ''}
    ''';

    return await _dbHelper.rawQuery(sql, whereArgs.isNotEmpty ? whereArgs : null);
  }

  /// الحصول على إحصائيات المديونيات
  Future<Map<String, dynamic>> getDebtsStatistics() async {
    final sql = '''
      SELECT 
        COUNT(*) as total_debts,
        COUNT(CASE WHEN status = '${AppConstants.debtStatusActive}' THEN 1 END) as active_debts,
        COUNT(CASE WHEN status = '${AppConstants.debtStatusPaid}' THEN 1 END) as paid_debts,
        COUNT(CASE WHEN status = '${AppConstants.debtStatusCancelled}' THEN 1 END) as cancelled_debts,
        COALESCE(SUM(amount), 0) as total_amount,
        COALESCE(SUM(CASE WHEN status = '${AppConstants.debtStatusActive}' THEN amount ELSE 0 END), 0) as active_amount,
        COALESCE(SUM(CASE WHEN status = '${AppConstants.debtStatusPaid}' THEN amount ELSE 0 END), 0) as paid_amount,
        COUNT(CASE WHEN person_type = '${AppConstants.personTypeCustomer}' THEN 1 END) as customer_debts,
        COUNT(CASE WHEN person_type = '${AppConstants.personTypeEmployee}' THEN 1 END) as employee_debts,
        COALESCE(SUM(CASE WHEN person_type = '${AppConstants.personTypeCustomer}' AND status = '${AppConstants.debtStatusActive}' THEN amount ELSE 0 END), 0) as customer_active_amount,
        COALESCE(SUM(CASE WHEN person_type = '${AppConstants.personTypeEmployee}' AND status = '${AppConstants.debtStatusActive}' THEN amount ELSE 0 END), 0) as employee_active_amount
      FROM ${AppConstants.debtsTable}
    ''';

    final results = await _dbHelper.rawQuery(sql);
    return results.first;
  }

  /// تحديث حالة مديونية
  Future<int> updateDebtStatus(int debtId, String newStatus) async {
    return await _dbHelper.update(
      AppConstants.debtsTable,
      {'status': newStatus},
      'id = ?',
      [debtId],
    );
  }

  /// تحديث حالة مديونيات متعددة
  Future<void> updateMultipleDebtsStatus(List<int> debtIds, String newStatus) async {
    final placeholders = debtIds.map((id) => '?').join(',');
    await _dbHelper.rawExecute(
      'UPDATE ${AppConstants.debtsTable} SET status = ?, updated_at = ? WHERE id IN ($placeholders)',
      [newStatus, DateTime.now().toIso8601String(), ...debtIds],
    );
  }

  /// الحصول على المديونيات في فترة زمنية محددة
  Future<List<Debt>> getDebtsByDateRange(
    DateTime startDate,
    DateTime endDate, {
    String? personType,
    String? status,
    String? orderBy,
    int? limit,
  }) async {
    String whereClause = 'date BETWEEN ? AND ?';
    List<dynamic> whereArgs = [
      startDate.toIso8601String(),
      endDate.toIso8601String(),
    ];

    if (personType != null && personType.isNotEmpty) {
      whereClause += ' AND person_type = ?';
      whereArgs.add(personType);
    }

    if (status != null && status.isNotEmpty) {
      whereClause += ' AND status = ?';
      whereArgs.add(status);
    }

    final results = await _dbHelper.query(
      AppConstants.debtsTable,
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: orderBy ?? 'date DESC',
      limit: limit,
    );

    return results.map((map) => Debt.fromMap(map)).toList();
  }

  /// فرز المديونيات حسب معايير مختلفة
  Future<List<Debt>> getSortedDebts({
    required String sortBy,
    bool ascending = true,
    String? personType,
    String? status,
    int? limit,
    int? offset,
  }) async {
    String orderBy;
    
    switch (sortBy.toLowerCase()) {
      case 'amount':
        orderBy = 'amount ${ascending ? 'ASC' : 'DESC'}';
        break;
      case 'date':
        orderBy = 'date ${ascending ? 'ASC' : 'DESC'}';
        break;
      case 'status':
        orderBy = 'status ${ascending ? 'ASC' : 'DESC'}';
        break;
      case 'person_type':
        orderBy = 'person_type ${ascending ? 'ASC' : 'DESC'}';
        break;
      case 'created_at':
        orderBy = 'created_at ${ascending ? 'ASC' : 'DESC'}';
        break;
      default:
        orderBy = 'date DESC';
    }

    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (personType != null && personType.isNotEmpty) {
      whereClause += 'person_type = ?';
      whereArgs.add(personType);
    }

    if (status != null && status.isNotEmpty) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'status = ?';
      whereArgs.add(status);
    }

    final results = await _dbHelper.query(
      AppConstants.debtsTable,
      where: whereClause.isNotEmpty ? whereClause : null,
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
      orderBy: orderBy,
      limit: limit,
      offset: offset,
    );

    return results.map((map) => Debt.fromMap(map)).toList();
  }
}
